import { GoogleGenAI, Type } from "@google/genai";
import { RiasecScores, OceanScores } from './types';



// Interface untuk response gabungan RIASEC + OCEAN
export interface CombinedProfileResponse {
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workStyle: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
}



// Fungsi untuk membuat prompt gabungan RIASEC + OCEAN
function createCombinedPrompt(riasecScores: RiasecScores, oceanScores: OceanScores): string {
  // Konversi skor RIASEC dari skala 5-25 ke skala 1-100
  const convertedRiasec = {
    R: Math.round((riasecScores.R / 25) * 100),
    I: Math.round((riasecScores.I / 25) * 100),
    A: Math.round((riasecScores.A / 25) * 100),
    S: Math.round((riasecScores.S / 25) * 100),
    E: Math.round((riasecScores.E / 25) * 100),
    C: Math.round((riasecScores.C / 25) * 100)
  };

  // Konversi skor OCEAN dari skala 5-25 ke skala 1-100
  const convertedOcean = {
    O: Math.round((oceanScores.O / 25) * 100),
    C: Math.round((oceanScores.C / 25) * 100),
    E: Math.round((oceanScores.E / 25) * 100),
    A: Math.round((oceanScores.A / 25) * 100),
    N: Math.round((oceanScores.N / 25) * 100)
  };

  return `Anda adalah seorang konselor karier ahli yang berspesialisasi dalam analisis kepribadian menggunakan model RIASEC (Holland Codes) dan Big Five (OCEAN).

Analisis hasil tes kepribadian gabungan berikut dan hasilkan profil yang komprehensif dan terintegrasi:

DATA SKOR RIASEC (Minat Karier - skala 1-100):
R (Realistic): ${convertedRiasec.R}
I (Investigative): ${convertedRiasec.I}
A (Artistic): ${convertedRiasec.A}
S (Social): ${convertedRiasec.S}
E (Enterprising): ${convertedRiasec.E}
C (Conventional): ${convertedRiasec.C}

DATA SKOR BIG FIVE/OCEAN (Kepribadian - skala 1-100):
O (Openness): ${convertedOcean.O}
C (Conscientiousness): ${convertedOcean.C}
E (Extraversion): ${convertedOcean.E}
A (Agreeableness): ${convertedOcean.A}
N (Neuroticism): ${convertedOcean.N}

Instruksi:
1. Kombinasikan insights dari kedua model untuk memberikan analisis yang lebih akurat
2. Fokus pada bagaimana kepribadian OCEAN mendukung atau melengkapi minat RIASEC
3. Berikan rekomendasi karier yang mempertimbangkan kedua aspek
4. Identifikasi area pengembangan berdasarkan kombinasi kedua profil
5. Gunakan bahasa Indonesia yang natural dan mudah dipahami siswa SMA
6. Berikan insights kepribadian yang spesifik berdasarkan kombinasi skor
7. Pastikan career fit menjelaskan mengapa kombinasi ini cocok untuk karier tertentu

Berikan analisis yang personal dan actionable berdasarkan kombinasi unik kedua profil ini.`;
}



// Schema untuk response gabungan RIASEC + OCEAN
const combinedProfileResponseSchema = {
  type: Type.OBJECT,
  properties: {
    profileTitle: {
      type: Type.STRING,
      description: "Judul profil yang menggabungkan insights RIASEC dan OCEAN (Dengan Bahasa inggris yang singkat)"
    },
    profileDescription: {
      type: Type.STRING,
      description: "Deskripsi kepribadian dalam DUA kalimat yang mengintegrasikan kedua model 2 kalimat singkat"
    },
    strengths: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 kekuatan utama berdasarkan kombinasi RIASEC dan OCEAN dalam 1 kalimat singkat"
    },
    careerSuggestions: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 rekomendasi karier yang mempertimbangkan minat dan kepribadian (use english)"
    },
    workStyle: {
      type: Type.STRING,
      description: "Gaya kerja ideal berdasarkan kombinasi kedua profil dalam 2 kalimat singkat"
    },
    developmentAreas: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 area pengembangan berdasarkan analisis gabungan dalam 2 kalimat singkat"
    },
    personalityInsights: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 insights kepribadian spesifik dari kombinasi OCEAN dan RIASEC dalam 2 kalimat singkat"
    },
    careerFit: {
      type: Type.STRING,
      description: "Penjelasan mengapa kombinasi profil ini cocok untuk karier yang direkomendasikan dalam 2 Kalimat"
    }
  },
  propertyOrdering: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle", "developmentAreas", "personalityInsights", "careerFit"],
  required: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle", "developmentAreas", "personalityInsights", "careerFit"]
};

// Service class untuk Gemini AI dengan structured output
export class GeminiProfileService {
  private ai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('NEXT_PUBLIC_GEMINI_API_KEY is not set in environment variables');
    }

    this.ai = new GoogleGenAI({
      apiKey: apiKey
    });
  }



  // Method baru untuk analisis gabungan RIASEC + OCEAN
  async generateCombinedProfile(riasecScores: RiasecScores, oceanScores: OceanScores): Promise<CombinedProfileResponse> {
    try {
      const prompt = createCombinedPrompt(riasecScores, oceanScores);

      // Menggunakan structured output dengan schema gabungan
      const response = await this.ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: prompt,
        config: {
          responseMimeType: "application/json",
          responseSchema: combinedProfileResponseSchema,
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
        }
      });

      const responseText = response.text;
      if (!responseText) {
        throw new Error('Empty response from Gemini AI');
      }

      const profileData: CombinedProfileResponse = JSON.parse(responseText);

      // Validasi response
      if (!profileData.profileTitle || !profileData.profileDescription ||
          !profileData.strengths || !profileData.careerSuggestions ||
          !profileData.workStyle || !profileData.developmentAreas ||
          !profileData.personalityInsights || !profileData.careerFit) {
        throw new Error('Invalid response structure from Gemini AI');
      }

      return profileData;
    } catch (error) {
      console.error('Error generating combined profile with Gemini AI:', error);

      // Fallback ke response default jika AI gagal
      return this.getCombinedFallbackProfile();
    }
  }

  // Fallback profile untuk analisis gabungan
  private getCombinedFallbackProfile(): CombinedProfileResponse {

    return {
      profileTitle: 'Profil Kepribadian Terintegrasi',
      profileDescription: 'Anda memiliki kombinasi unik antara minat karier dan kepribadian yang dapat dikembangkan lebih lanjut. Profil ini menggabungkan aspek minat dan karakteristik kepribadian Anda.',
      strengths: [
        'Kemampuan adaptasi yang baik',
        'Potensi pengembangan yang beragam',
        'Fleksibilitas dalam pendekatan',
        'Keseimbangan antara minat dan kepribadian',
        'Kapasitas untuk pertumbuhan personal'
      ],
      careerSuggestions: [
        'Konsultan multidisiplin',
        'Koordinator proyek',
        'Spesialis pengembangan',
        'Analis sistem',
        'Manajer operasional'
      ],
      workStyle: 'Lingkungan kerja yang seimbang dengan variasi tugas dan interaksi yang disesuaikan dengan preferensi personal.',
      developmentAreas: [
        'Pengembangan keterampilan komunikasi',
        'Peningkatan kemampuan analitis',
        'Penguatan kepercayaan diri',
        'Pengembangan keterampilan teknis'
      ],
      personalityInsights: [
        'Memiliki potensi untuk berkembang di berbagai bidang',
        'Dapat menyesuaikan diri dengan berbagai situasi kerja',
        'Membutuhkan lingkungan yang mendukung pertumbuhan',
        'Cocok untuk peran yang memerlukan fleksibilitas'
      ],
      careerFit: 'Kombinasi profil Anda menunjukkan fleksibilitas dan adaptabilitas yang tinggi, cocok untuk karier yang memerlukan keseimbangan antara berbagai keterampilan dan kemampuan interpersonal.'
    };
  }
}
