import { OceanScores } from '@/lib/types';
import { oceanDescriptions } from '@/lib/oceanQuestions';

interface OceanChartProps {
  scores: OceanScores;
}

// Helper function to get trait level
function getTraitLevel(score: number): { level: string; color: string; percentage: number } {
  const percentage = (score / 50) * 100; // Convert to percentage (max score is 50)

  if (score <= 25) {
    return {
      level: 'Low',
      color: 'bg-red-500',
      percentage
    };
  } else if (score <= 35) {
    return {
      level: 'Medium',
      color: 'bg-yellow-500',
      percentage
    };
  } else {
    return {
      level: 'High',
      color: 'bg-green-500',
      percentage
    };
  }
}

// Helper function to get trait icon
function getTraitIcon(trait: string): string {
  const icons = {
    'O': '🎨',
    'C': '📋',
    'E': '🎉',
    'A': '🤝',
    'N': '😰'
  };
  return icons[trait as keyof typeof icons] || '📊';
}

export default function OceanChart({ scores }: OceanChartProps) {
  const traits = Object.entries(scores).map(([trait, score]) => {
    const traitInfo = getTraitLevel(score);
    const description = oceanDescriptions.find(d => d.type === trait);

    return {
      trait,
      name: trait, // Now using English abbreviation directly
      score,
      level: traitInfo.level,
      color: traitInfo.color,
      percentage: traitInfo.percentage,
      icon: getTraitIcon(trait),
      description: description?.description || ''
    };
  });

  return (
    <div className="bg-white rounded-2xl shadow-xl p-8">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
        Big Five Personality Profile (OCEAN)
      </h2>

      <div className="space-y-6">
        {traits.map((trait) => (
          <div key={trait.trait} className="relative">
            {/* Trait Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <span className="text-2xl mr-3">{trait.icon}</span>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">
                    {trait.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {trait.description.substring(0, 80)}...
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-800">
                  {trait.score}
                </div>
                <div className={`text-sm font-medium px-2 py-1 rounded-full text-white ${
                  trait.level === 'High' ? 'bg-green-500' :
                  trait.level === 'Medium' ? 'bg-yellow-500' : 'bg-red-500'
                }`}>
                  {trait.level}
                </div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
              <div
                className={`h-4 rounded-full transition-all duration-1000 ease-out ${trait.color}`}
                style={{ width: `${trait.percentage}%` }}
              ></div>
            </div>

            {/* Score Range Indicator */}
            <div className="flex justify-between text-xs text-gray-500 mb-4">
              <span>10 (Low)</span>
              <span>30 (Medium)</span>
              <span>50 (High)</span>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Info */}
      <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
        <div className="flex items-start">
          <span className="text-blue-500 text-xl mr-3">💡</span>
          <div>
            <h4 className="text-sm font-semibold text-blue-800 mb-1">About Big Five (OCEAN)</h4>
            <p className="text-sm text-blue-700">
              The Big Five model measures five core dimensions of personality that are stable and universal. 
              Each dimension represents a spectrum of personality characteristics and helps understand your 
              behavioral patterns, preferences, and interaction styles.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}